import { useEffect, useState, useCallback, useRef } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  FileText,
  Edit,
  Loader2,
  ClipboardCheck,
  Calendar,
  Users,
  ListChecks,
  FileBarChart,
  Activity,
  GitBranch,
  LayoutDashboard
} from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { toast } from "sonner";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { fetchAuditPlanById, clearCurrentPlan } from '@/store/slices/auditPlanSlice';

function EditPlansDaudit() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const abortControllerRef = useRef(null);
  
  // Get state from Redux with proper selector
  const { currentPlan, loading, error, lastFetched } = useSelector((state) => ({
    currentPlan: state.auditPlans?.currentPlan,
    loading: state.auditPlans?.loading,
    error: state.auditPlans?.error,
    lastFetched: state.auditPlans?.lastFetched
  }));

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/caracteristiques')) return 'caracteristiques';
    if (path.includes('/missions-audits')) return 'missions-audits';
    if (path.includes('/planification')) return 'planification';
    if (path.includes('/affectation-ressources')) return 'affectation-ressources';
    if (path.includes('/recommandations')) return 'recommandations';
    if (path.includes('/rapports')) return 'rapports';
    if (path.includes('/fil-activite')) return 'fil-activite';
    if (path.includes('/workflow')) return 'workflow';
    return 'vue-ensemble'; // Default tab
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch audit plan data only if not already in store or if it's a different plan
  useEffect(() => {
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController for this request
    abortControllerRef.current = new AbortController();

    if (id && (!currentPlan || currentPlan.id !== id || !lastFetched)) {
      const planId = id.toString(); // Ensure id is a string
      dispatch(fetchAuditPlanById({ 
        id: planId,
        signal: abortControllerRef.current.signal 
      }))
        .unwrap()
        .catch((error) => {
          if (error.name !== 'CanceledError') {
            toast.error(error || 'Failed to fetch audit plan');
            navigate('/audit/plans-daudit');
          }
        });
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      // Only clear if navigating away from the edit page
      if (!location.pathname.includes('/audit/plans-daudit/')) {
        dispatch(clearCurrentPlan());
      }
    };
  }, [id, dispatch, navigate, currentPlan, lastFetched, location.pathname]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/audit/plans-daudit");
  };

  // Define tabs
  const tabs = [
    { id: 'vue-ensemble', label: 'Vue d\'ensemble', icon: <LayoutDashboard className="h-4 w-4 mr-2" /> },
    { id: 'caracteristiques', label: 'Caractéristiques', icon: <FileText className="h-4 w-4 mr-2" /> },
    { id: 'missions-audits', label: 'Missions d\'audit', icon: <ClipboardCheck className="h-4 w-4 mr-2" /> },
    { id: 'planification', label: 'Planification', icon: <Calendar className="h-4 w-4 mr-2" /> },
    { id: 'affectation-ressources', label: 'Affectation des ressources', icon: <Users className="h-4 w-4 mr-2" /> },
    { id: 'recommandations', label: 'Recommandations', icon: <ListChecks className="h-4 w-4 mr-2" /> },
    { id: 'rapports', label: 'Rapports', icon: <FileBarChart className="h-4 w-4 mr-2" /> },
    { id: 'fil-activite', label: 'Fil d\'activité', icon: <Activity className="h-4 w-4 mr-2" /> },
    { id: 'workflow', label: 'Workflow', icon: <GitBranch className="h-4 w-4 mr-2" /> }
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "vue-ensemble":
        navigate(`/audit/plans-daudit/${id}`);
        break;
      case "caracteristiques":
        navigate(`/audit/plans-daudit/${id}/caracteristiques`);
        break;
      case "missions-audits":
        navigate(`/audit/plans-daudit/${id}/missions-audits`);
        break;
      case "planification":
        navigate(`/audit/plans-daudit/${id}/planification`);
        break;
      case "affectation-ressources":
        navigate(`/audit/plans-daudit/${id}/affectation-ressources`);
        break;
      case "recommandations":
        navigate(`/audit/plans-daudit/${id}/recommandations`);
        break;
      case "rapports":
        navigate(`/audit/plans-daudit/${id}/rapports`);
        break;
      case "fil-activite":
        navigate(`/audit/plans-daudit/${id}/fil-activite`);
        break;
      case "workflow":
        navigate(`/audit/plans-daudit/${id}/workflow`);
        break;
      default:
        navigate(`/audit/plans-daudit/${id}`);
    }
  };

  if (loading && !currentPlan) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  if (error || !currentPlan) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error || 'Plan d\'audit non trouvé'}. <button className="underline" onClick={handleGoBack}>Retour à la liste</button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Main content container with single scroll */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-[1200px] mx-auto">
          {/* Header Section - removed bg-white */}
          <div className="sticky top-0 z-10 border-b border-gray-200">
            <div className="px-6">
      {/* Header with breadcrumb */}
      <DetailHeader
                title={currentPlan.name}
        icon={<ClipboardCheck className="h-6 w-6 text-[#F62D51]" />}
        metadata={[
                  currentPlan.director?.username || 'Non assigné',
                  `Statut: ${currentPlan.status}`,
                  `Année: ${currentPlan.calendrier}`,
                  currentPlan.datedebut && currentPlan.datefin ?
                    `Période: ${new Date(currentPlan.datedebut).toLocaleDateString()} - ${new Date(currentPlan.datefin).toLocaleDateString()}` :
            null
        ].filter(Boolean)}
        onBack={handleGoBack}
        backLabel="Retour aux plans d'audit"
        breadcrumb={
          <Breadcrumb>
            <BreadcrumbList className="text-sm">
              <BreadcrumbItem>
                <BreadcrumbLink
                  href="/audit/plans-daudit"
                  className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/audit/plans-daudit');
                  }}
                >
                  Audit
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-gray-800 font-medium">{currentPlan?.name || 'Plan d\'audit'}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        }
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />
            </div>
          </div>

          {/* Content Section */}
          <div className="px-6 py-6">
      <TabContent>
              <Outlet context={{ auditPlan: currentPlan }} />
      </TabContent>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditPlansDaudit;