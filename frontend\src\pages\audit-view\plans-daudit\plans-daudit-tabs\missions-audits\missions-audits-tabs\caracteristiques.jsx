import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from 'react';
import { debounce } from "lodash";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  ClipboardList, 
  Save, 
  FileSymlink, 
  ChevronDown, 
  ChevronUp, 
  FileText, 
  Calendar, 
  Users, 
  Clock, 
  BarChart, 
  Target, 
  CheckSquare, 
  Briefcase,
  AlertTriangle,
  CheckCircle,
  CircleAlert,
  User,
  Search
} from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { Checkbox } from "@/components/ui/checkbox";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Add Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error in component:', error);
    console.error('Error info:', errorInfo);
    toast.error('Une erreur est survenue dans le composant');
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-500 rounded bg-red-50">
          <h2 className="text-red-700 font-bold">Une erreur est survenue</h2>
          <pre className="text-sm text-red-600 mt-2">
            {this.state.error?.toString()}
          </pre>
          <Button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2"
          >
            Réessayer
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

function CaracteristiquesTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const { refetchMissionAudit, isRefreshing } = contextData || {};
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const isMountedRef = useRef(true);
  const [users, setUsers] = useState([]);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isAuditedModalOpen, setIsAuditedModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [auditedSearchTerm, setAuditedSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedAudited, setSelectedAudited] = useState(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const [codeValidation, setCodeValidation] = useState({ isValid: true, message: '', isChecking: false });

  // Section states
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isMotivationsOpen, setIsMotivationsOpen] = useState(true);
  const [isJalonsOpen, setIsJalonsOpen] = useState(true);
  const [isResponsabilitesOpen, setIsResponsabilitesOpen] = useState(true);
  const [isCompetencesOpen, setIsCompetencesOpen] = useState(true);
  const [isConclusionOpen, setIsConclusionOpen] = useState(true);
  
  // Initialize characteristics with safe defaults
  const [characteristics, setCharacteristics] = useState({
    title: '',
    code: '',
    category: '',
    status: 'En cours',
    auditPlan: '',
    includedInInitialPlan: false,
    leadAuditor: '',
    mainAudited: '',
    mainAuditedId: '',
    objectives: '',
    pointsForts: '',
    pointsFaibles: '',
    evaluationLevel: 'Bon niveau',
    progression: 0,
    evaluation: 'Bon niveau',
    recommendations: 0
  });

  // Optimized user fetch with error handling
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const response = await axios.get(`${getApiBaseUrl()}/users`);
        if (response.data.success && Array.isArray(response.data.data)) {
          setUsers(response.data.data);
        } else {
          setUsers([]);
          toast.error('Données des utilisateurs non valides');
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        setUsers([]);
        toast.error('Erreur lors du chargement des utilisateurs');
      } finally {
        setIsLoadingUsers(false);
      }
    };
    fetchUsers();
  }, []);

  // Update characteristics when mission data changes
  useEffect(() => {
    if (missionAudit && !isLoadingUsers) {
      console.log('=== DEBUG: Mission Audit Data ===');
      console.log('Full missionAudit:', missionAudit);
      console.log('chefmission:', missionAudit.chefmission, 'type:', typeof missionAudit.chefmission);
      console.log('principalAudite:', missionAudit.principalAudite, 'type:', typeof missionAudit.principalAudite);

      // Handle leadAuditor assignment - only use chefmission (integer)
      let leadAuditorValue = '';
      let selectedUserData = null;

      // Only use chefmission if it exists (this is the integer field from the database)
      if (missionAudit.chefmission !== null && missionAudit.chefmission !== undefined) {
        leadAuditorValue = missionAudit.chefmission.toString();
        console.log('Using chefmission:', leadAuditorValue);

        // Find the user data for display from the users array
        const userData = users.find(user => user.id === missionAudit.chefmission);
        if (userData) {
          selectedUserData = userData;
        }
      }

      // Handle principal audité assignment
      let mainAuditedValue = '';
      let mainAuditedIdValue = '';
      let selectedAuditedData = null;

      // Check if principalAudite is a user ID (integer) or a text field
      if (missionAudit.principalAudite !== null && missionAudit.principalAudite !== undefined) {
        const principalAuditeStr = missionAudit.principalAudite.toString();
        // Try to parse as integer to see if it's a user ID
        const principalAuditeInt = parseInt(principalAuditeStr);

        if (!isNaN(principalAuditeInt) && principalAuditeInt.toString() === principalAuditeStr) {
          // It's a user ID
          mainAuditedIdValue = principalAuditeStr;
          const auditedUserData = users.find(user => user.id === principalAuditeInt);
          if (auditedUserData) {
            selectedAuditedData = auditedUserData;
            mainAuditedValue = auditedUserData.username || auditedUserData.email;
          }
        } else {
          // It's a text field
          mainAuditedValue = principalAuditeStr;
        }
      }

      console.log('Final leadAuditorValue:', leadAuditorValue, 'type:', typeof leadAuditorValue);
      console.log('Selected user data:', selectedUserData);
      console.log('Final mainAuditedValue:', mainAuditedValue, 'mainAuditedIdValue:', mainAuditedIdValue);
      console.log('Selected audited data:', selectedAuditedData);
      console.log('=== END DEBUG ===');

      setSelectedUser(selectedUserData);
      setSelectedAudited(selectedAuditedData);
      
      setCharacteristics({
        title: missionAudit.name || '',
        code: missionAudit.code || '',
        category: missionAudit.categorie || '',
        status: missionAudit.etat || 'En cours',
        auditPlan: missionAudit.auditPlan?.name || '',
        includedInInitialPlan: missionAudit.planifieInitialement || false,
        leadAuditor: leadAuditorValue,
        mainAudited: mainAuditedValue,
        mainAuditedId: mainAuditedIdValue,
        objectives: missionAudit.objectif || '',
        pointsForts: missionAudit.pointfort || '',
        pointsFaibles: missionAudit.pointfaible || '',
        evaluationLevel: missionAudit.evaluation || 'Bon niveau',
        progression: parseInt(missionAudit.avancement) || 0,
        evaluation: missionAudit.evaluation || 'Bon niveau',
        recommendations: 0
      });
    }
  }, [missionAudit, users, isLoadingUsers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Auto-save function
  const autoSave = useCallback(async (currentCharacteristics) => {
    if (!missionAudit?.id || !isMountedRef.current) {
      return;
    }

    // Validate required fields before saving
    if (!currentCharacteristics.title.trim()) {
      return; // Don't save if required fields are empty
    }

    // Don't save if code is invalid
    if (!codeValidation.isValid && currentCharacteristics.code.trim()) {
      return;
    }

    setIsAutoSaving(true);
    try {
      // Determine principal audité value - use ID if selected from users, otherwise use text
      const principalAuditeValue = currentCharacteristics.mainAuditedId
        ? parseInt(currentCharacteristics.mainAuditedId)
        : currentCharacteristics.mainAudited;

      const missionData = {
        name: currentCharacteristics.title,
        code: currentCharacteristics.code,
        categorie: currentCharacteristics.category,
        etat: currentCharacteristics.status,
        chefmission: currentCharacteristics.leadAuditor === '' ? null : parseInt(currentCharacteristics.leadAuditor),
        principalAudite: principalAuditeValue,
        objectif: currentCharacteristics.objectives,
        pointfort: currentCharacteristics.pointsForts,
        pointfaible: currentCharacteristics.pointsFaibles,
        evaluation: currentCharacteristics.evaluationLevel,
        avancement: currentCharacteristics.progression.toString(),
        planifieInitialement: currentCharacteristics.includedInInitialPlan
      };

      const response = await axios.put(
        `${getApiBaseUrl()}/audit-missions/${missionAudit.id}`,
        missionData
      );

      if (response.data.success && isMountedRef.current) {
        // Update the local state with the response data
        setCharacteristics(prev => ({
          ...prev,
          title: response.data.data.name,
          code: response.data.data.code,
          category: response.data.data.categorie,
          status: response.data.data.etat,
          leadAuditor: response.data.data.chefmission ? response.data.data.chefmission.toString() : '',
          mainAudited: response.data.data.principalAudite,
          objectives: response.data.data.objectif,
          pointsForts: response.data.data.pointfort,
          pointsFaibles: response.data.data.pointfaible,
          evaluationLevel: response.data.data.evaluation,
          progression: parseInt(response.data.data.avancement) || 0,
          includedInInitialPlan: response.data.data.planifieInitialement
        }));

        // Refresh the parent context to update the header
        if (refetchMissionAudit) {
          await refetchMissionAudit();
        }
      } else if (isMountedRef.current) {
        throw new Error(response.data.message || 'Erreur lors de la sauvegarde automatique');
      }
    } catch (error) {
      if (isMountedRef.current) {
        console.error('Error auto-saving mission audit:', error);
        toast.error('Erreur lors de la sauvegarde automatique');
      }
    } finally {
      if (isMountedRef.current) {
        setIsAutoSaving(false);
      }
    }
  }, [missionAudit?.id, refetchMissionAudit, codeValidation.isValid]);

  // Debounced auto-save function
  const debouncedAutoSave = useMemo(
    () => debounce(autoSave, 1500), // 1.5 second debounce delay
    [autoSave]
  );

  // Code validation function
  const validateCode = useCallback(async (code) => {
    if (!code.trim()) {
      setCodeValidation({ isValid: true, message: '', isChecking: false });
      return true;
    }

    setCodeValidation(prev => ({ ...prev, isChecking: true }));

    try {
      const response = await axios.get(`${getApiBaseUrl()}/audit-missions/validate-code/${encodeURIComponent(code)}`, {
        params: { excludeId: missionAudit?.id }
      });

      const isValid = response.data.success && response.data.isUnique;
      setCodeValidation({
        isValid,
        message: isValid ? '' : 'Ce code est déjà utilisé par une autre mission',
        isChecking: false
      });

      return isValid;
    } catch (error) {
      console.error('Error validating code:', error);
      setCodeValidation({
        isValid: false,
        message: 'Erreur lors de la validation du code',
        isChecking: false
      });
      return false;
    }
  }, [missionAudit?.id]);

  // Debounced code validation
  const debouncedCodeValidation = useMemo(
    () => debounce(validateCode, 800), // 800ms delay for validation
    [validateCode]
  );

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter users for audited selection
  const filteredAuditedUsers = users.filter(user =>
    user.username?.toLowerCase().includes(auditedSearchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle user selection for chef de mission
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, leadAuditor: user.id.toString() };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
    setIsUserModalOpen(false);
    setSearchTerm('');
  };

  // Handle user selection for principal audité
  const handleAuditedUserSelect = (user) => {
    setSelectedAudited(user);
    setCharacteristics(prev => {
      const newCharacteristics = {
        ...prev,
        mainAudited: user.username || user.email,
        mainAuditedId: user.id.toString()
      };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
    setIsAuditedModalOpen(false);
    setAuditedSearchTerm('');
  };

  // Handle user removal for chef de mission
  const handleUserRemove = () => {
    setSelectedUser(null);
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, leadAuditor: '' };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  // Handle user removal for principal audité
  const handleAuditedUserRemove = () => {
    setSelectedAudited(null);
    setCharacteristics(prev => {
      const newCharacteristics = {
        ...prev,
        mainAudited: '',
        mainAuditedId: ''
      };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  // If no mission data is available yet or users are loading, show loading
  if (!missionAudit || !missionAudit.id || isLoadingUsers) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
            <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
            Caractéristiques de la Mission
          </h2>
          <div className="flex items-center gap-2">
            <div className="flex items-center text-sm text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600 mr-2" />
              Chargement...
            </div>
          </div>
        </div>

        {/* Loading skeleton for caracteristiques section */}
        <div className="border rounded-lg shadow-sm">
          <div className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
            <div className="flex items-center gap-2">
              <ChevronUp className="h-5 w-5 text-blue-600" />
              <FileText className="h-5 w-5 text-blue-600 mr-1" />
              <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
            </div>
          </div>
          <div className="p-5 bg-white space-y-6">
            <div className="grid grid-cols-4 gap-4">
              {/* Loading skeleton content */}
              {[...Array(8)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="h-10 bg-gray-100 rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }



  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: value };

      // Special handling for code field - trigger validation
      if (name === 'code') {
        debouncedCodeValidation(value);
      }

      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  const handleSelectChange = (name, value) => {
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: value };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  const handleCheckboxChange = (name, checked) => {
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, [name]: checked };
      // Trigger auto-save
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };

  const renderChefDeMission = () => {
    return (
      <div className="col-span-2 space-y-2">
        <Label htmlFor="leadAuditor">Chef de mission</Label>
        <div className="flex gap-2">
          <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="flex-1 justify-start"
                type="button"
                disabled={isLoadingUsers}
              >
                {isLoadingUsers ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                    <span>Chargement des utilisateurs...</span>
                  </div>
                ) : selectedUser ? (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{selectedUser.username || selectedUser.email}</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>Sélectionner un chef de mission</span>
                  </div>
                )}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Sélectionner un chef de mission</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher un utilisateur..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {isLoadingUsers ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                      <span className="ml-2 text-gray-500">Chargement des utilisateurs...</span>
                    </div>
                  ) : filteredUsers.length > 0 ? (
                    filteredUsers.map(user => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleUserSelect(user)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{user.username}</p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                          </div>
                        </div>
                        {selectedUser?.id === user.id && (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                      </div>
                    ))
                  ) : (
                    <p className="text-center text-gray-500 py-4">
                      {searchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                    </p>
                  )}
                </div>
              </div>
            </DialogContent>
          </Dialog>
          {selectedUser && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleUserRemove}
              className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
              type="button"
            >
              ×
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
            <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
            Caractéristiques de la Mission
          </h2>
          <div className="flex items-center gap-2">
            {isAutoSaving && (
              <div className="flex items-center text-sm text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600 mr-2" />
                Sauvegarde automatique...
              </div>
            )}
            {isRefreshing && (
              <div className="flex items-center text-sm text-gray-600">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600 mr-2" />
                Mise à jour...
              </div>
            )}
          </div>
        </div>

        {/* Section Caractéristiques */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
            onClick={() => setIsCaracteristiquesOpen(!isCaracteristiquesOpen)}
          >
            <div className="flex items-center gap-2">
              {isCaracteristiquesOpen ? (
                <ChevronUp className="h-5 w-5 text-blue-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-blue-600" />
              )}
              <FileText className="h-5 w-5 text-blue-600 mr-1" />
              <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
            </div>
          </button>

          {isCaracteristiquesOpen && (
            <div className="p-5 bg-white">
              <div className="grid grid-cols-4 gap-4">
                {/* First row */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="title">Nom *</Label>
                  <Input 
                    id="title" 
                    name="title"
                    value={characteristics.title}
                    onChange={handleInputChange}
                    placeholder="Nom de la mission d'audit"
                    className="w-full"
                  />
                </div>
                  
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="code">Code *</Label>
                  <div className="relative">
                    <Input
                      id="code"
                      name="code"
                      value={characteristics.code}
                      onChange={handleInputChange}
                      placeholder="Code"
                      className={`w-full ${
                        !codeValidation.isValid ? 'border-red-500 focus:border-red-500' : ''
                      }`}
                    />
                    {codeValidation.isChecking && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-400" />
                      </div>
                    )}
                  </div>
                  {!codeValidation.isValid && codeValidation.message && (
                    <p className="text-sm text-red-600">{codeValidation.message}</p>
                  )}
                </div>
                  
                {/* Second row */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="category">Catégorie</Label>
                  <Select 
                    name="category"
                    value={characteristics.category} 
                    onValueChange={(value) => handleSelectChange("category", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="category" className="w-full">
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Conformité">Conformité</SelectItem>
                      <SelectItem value="Performance">Efficacité</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  
                <div className="col-span-1 space-y-2">
                  <Label htmlFor="status">État</Label>
                  <Select 
                    name="status"
                    value={characteristics.status} 
                    onValueChange={(value) => handleSelectChange("status", value)}
                    className="w-full"
                  >
                    <SelectTrigger id="status" className="w-full">
                      <SelectValue placeholder="Sélectionner un statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Planifiée">Planifiée</SelectItem>
                      <SelectItem value="En cours">En cours</SelectItem>
                      <SelectItem value="Terminée">Terminée</SelectItem>
                      <SelectItem value="Suspendue">Suspendue</SelectItem>
                      <SelectItem value="Annulée">Annulée</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                  
                {/* Third row */}
                <div className="col-span-3 space-y-2">
                  <Label htmlFor="auditPlan">Plan d'audit</Label>
                  <Input 
                    id="auditPlan" 
                    name="auditPlan"
                    value={characteristics.auditPlan}
                    readOnly
                    placeholder="Plan d'audit (lecture seule)"
                    className="w-full bg-gray-50 cursor-not-allowed"
                  />
                </div>

                <div className="col-span-1 flex items-end space-y-2">
                  <div className="flex items-center space-x-2 h-10 pt-4 w-full">
                    <Checkbox 
                      id="includedInInitialPlan" 
                      checked={characteristics.includedInInitialPlan}
                      onCheckedChange={(checked) => handleCheckboxChange("includedInInitialPlan", checked)}
                    />
                    <Label htmlFor="includedInInitialPlan">Inclus dans le plan initial</Label>
                  </div>
                </div>
              
                {/* Fourth row - Chef de mission */}
                {renderChefDeMission()}
                
                <div className="col-span-2 space-y-2">
                  <Label htmlFor="mainAudited">Principal audité</Label>
                  <div className="flex gap-2">
                    <Dialog open={isAuditedModalOpen} onOpenChange={setIsAuditedModalOpen}>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="flex-1 justify-start"
                          type="button"
                          disabled={isLoadingUsers}
                        >
                          {isLoadingUsers ? (
                            <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                              <span>Chargement des utilisateurs...</span>
                            </div>
                          ) : selectedAudited ? (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{selectedAudited.username || selectedAudited.email}</span>
                            </div>
                          ) : characteristics.mainAudited ? (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>{characteristics.mainAudited}</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>Sélectionner un principal audité</span>
                            </div>
                          )}
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-md">
                        <DialogHeader>
                          <DialogTitle>Sélectionner un principal audité</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="relative">
                            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              placeholder="Rechercher un utilisateur..."
                              value={auditedSearchTerm}
                              onChange={(e) => setAuditedSearchTerm(e.target.value)}
                              className="pl-10"
                            />
                          </div>
                          <div className="max-h-60 overflow-y-auto space-y-2">
                            {isLoadingUsers ? (
                              <div className="flex items-center justify-center py-8">
                                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                                <span className="ml-2 text-gray-500">Chargement des utilisateurs...</span>
                              </div>
                            ) : filteredAuditedUsers.length > 0 ? (
                              filteredAuditedUsers.map(user => (
                                <div
                                  key={user.id}
                                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                  onClick={() => handleAuditedUserSelect(user)}
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                      <User className="h-4 w-4 text-blue-600" />
                                    </div>
                                    <div>
                                      <p className="font-medium">{user.username}</p>
                                      <p className="text-sm text-gray-500">{user.email}</p>
                                    </div>
                                  </div>
                                  {selectedAudited?.id === user.id && (
                                    <CheckCircle className="h-5 w-5 text-green-500" />
                                  )}
                                </div>
                              ))
                            ) : (
                              <p className="text-center text-gray-500 py-4">
                                {auditedSearchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                              </p>
                            )}
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    {(selectedAudited || characteristics.mainAudited) && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleAuditedUserRemove}
                        className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                        type="button"
                      >
                        ×
                      </Button>
                    )}
                  </div>
                </div>
                
                {/* Fifth row */}
                <div className="col-span-4 space-y-2">
                  <Label htmlFor="objectives">Objectif *</Label>
                  <Textarea 
                    id="objectives" 
                    name="objectives"
                    value={characteristics.objectives}
                    onChange={handleInputChange}
                    placeholder="Objectifs principaux de la mission d'audit"
                    rows={3}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Section Motivations et Charge */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
            onClick={() => setIsMotivationsOpen(!isMotivationsOpen)}
          >
            <div className="flex items-center gap-2">
              {isMotivationsOpen ? (
                <ChevronUp className="h-5 w-5 text-green-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-green-600" />
              )}
              <Target className="h-5 w-5 text-green-600 mr-1" />
              <span className="text-lg font-medium text-green-800">Motivations et Charge</span>
            </div>
          </button>

          {isMotivationsOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Jalons */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-t-lg"
            onClick={() => setIsJalonsOpen(!isJalonsOpen)}
          >
            <div className="flex items-center gap-2">
              {isJalonsOpen ? (
                <ChevronUp className="h-5 w-5 text-amber-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-amber-600" />
              )}
              <Calendar className="h-5 w-5 text-amber-600 mr-1" />
              <span className="text-lg font-medium text-amber-800">Jalons</span>
            </div>
          </button>

          {isJalonsOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Responsabilités */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg"
            onClick={() => setIsResponsabilitesOpen(!isResponsabilitesOpen)}
          >
            <div className="flex items-center gap-2">
              {isResponsabilitesOpen ? (
                <ChevronUp className="h-5 w-5 text-purple-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-purple-600" />
              )}
              <Users className="h-5 w-5 text-purple-600 mr-1" />
              <span className="text-lg font-medium text-purple-800">Responsabilités</span>
            </div>
          </button>

          {isResponsabilitesOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Compétences */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-teal-50 to-cyan-50 rounded-t-lg"
            onClick={() => setIsCompetencesOpen(!isCompetencesOpen)}
          >
            <div className="flex items-center gap-2">
              {isCompetencesOpen ? (
                <ChevronUp className="h-5 w-5 text-teal-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-teal-600" />
              )}
              <Briefcase className="h-5 w-5 text-teal-600 mr-1" />
              <span className="text-lg font-medium text-teal-800">Compétences</span>
            </div>
          </button>

          {isCompetencesOpen && (
            <div className="p-5 bg-white">
              <div className="flex items-center justify-center">
                <p className="text-gray-500 italic">Cette section est vide</p>
              </div>
            </div>
          )}
        </div>

        {/* Section Conclusion */}
        <div className="border rounded-lg shadow-sm">
          <button
            type="button"
            className="w-full flex items-center p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-t-lg"
            onClick={() => setIsConclusionOpen(!isConclusionOpen)}
          >
            <div className="flex items-center gap-2">
              {isConclusionOpen ? (
                <ChevronUp className="h-5 w-5 text-red-600" />
              ) : (
                <ChevronDown className="h-5 w-5 text-red-600" />
              )}
              <CheckSquare className="h-5 w-5 text-red-600 mr-1" />
              <span className="text-lg font-medium text-red-800">Conclusion</span>
            </div>
          </button>

          {isConclusionOpen && (
            <div className="p-5 bg-white">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="pointsForts">Points forts clés</Label>
                  <Textarea 
                    id="pointsForts" 
                    name="pointsForts"
                    value={characteristics.pointsForts}
                    onChange={handleInputChange}
                    placeholder="Points forts identifiés lors de l'audit"
                    rows={4}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="pointsFaibles">Points faibles clés</Label>
                  <Textarea 
                    id="pointsFaibles" 
                    name="pointsFaibles"
                    value={characteristics.pointsFaibles}
                    onChange={handleInputChange}
                    placeholder="Points faibles identifiés lors de l'audit"
                    rows={4}
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-3">
                  <Label>Évaluation globale</Label>
                  <Select 
                    name="evaluationLevel"
                    value={characteristics.evaluationLevel} 
                    onValueChange={(value) => handleSelectChange("evaluationLevel", value)}
                    className="w-full"
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Sélectionner une évaluation" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Bon niveau" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-green-500 mr-2 inline-block"></span>
                          Bon niveau
                        </div>
                      </SelectItem>
                      <SelectItem value="Peut être amélioré" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-yellow-500 mr-2 inline-block"></span>
                          Peut être amélioré
                        </div>
                      </SelectItem>
                      <SelectItem value="Amélioration nécessaire" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-orange-500 mr-2 inline-block"></span>
                          Amélioration nécessaire
                        </div>
                      </SelectItem>
                      <SelectItem value="A risque" className="flex items-center">
                        <div className="flex items-center">
                          <span className="h-3 w-3 rounded-full bg-red-500 mr-2 inline-block"></span>
                          A risque
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default CaracteristiquesTab; 