import { useOutletContext, useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { Users, Search, Mail, Phone, MapPin, Briefcase, Loader2, AlertCircle, UserCheck } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";

function AffectationRessourcesTab() {
  const { auditPlan } = useOutletContext();
  const { id: planId } = useParams();

  const [assignedUsers, setAssignedUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch all users assigned to any module of this specific plan
  useEffect(() => {
    const fetchAssignedUsers = async () => {
      if (!planId) return;

      try {
        setLoading(true);

        // Fetch missions for this plan
        const missionsResponse = await axios.get(`${getApiBaseUrl()}/audit-missions/plan/${planId}`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });

        let allAssignedUsers = [];

        if (missionsResponse.data.success && missionsResponse.data.data) {
          const missions = missionsResponse.data.data;

          // Collect users from missions
          for (const mission of missions) {
            if (mission.chefmission) {
              allAssignedUsers.push({
                userId: mission.chefmission,
                role: 'Chef de mission',
                module: 'Mission',
                moduleName: mission.name,
                moduleId: mission.id
              });
            }
            if (mission.principalAudite) {
              allAssignedUsers.push({
                userId: mission.principalAudite,
                role: 'Principal audité',
                module: 'Mission',
                moduleName: mission.name,
                moduleId: mission.id
              });
            }

            // Fetch activities for each mission
            try {
              const activitiesResponse = await axios.get(`${getApiBaseUrl()}/audit-activities/mission/${mission.id}`, {
                withCredentials: true,
                headers: { 'Content-Type': 'application/json' }
              });

              if (activitiesResponse.data.success && activitiesResponse.data.data) {
                const activities = activitiesResponse.data.data;

                for (const activity of activities) {
                  if (activity.responsable) {
                    allAssignedUsers.push({
                      userId: activity.responsable,
                      role: 'Responsable',
                      module: 'Activité',
                      moduleName: activity.name,
                      moduleId: activity.id
                    });
                  }
                }
              }
            } catch (activityError) {
              console.error(`Error fetching activities for mission ${mission.id}:`, activityError);
            }
          }
        }

        // Fetch user details for all assigned users
        const userIds = [...new Set(allAssignedUsers.map(u => u.userId))];
        const usersWithDetails = [];

        for (const userId of userIds) {
          try {
            const userResponse = await axios.get(`${getApiBaseUrl()}/users/${userId}`, {
              withCredentials: true,
              headers: { 'Content-Type': 'application/json' }
            });

            if (userResponse.data.success) {
              const user = userResponse.data.data;
              const userAssignments = allAssignedUsers.filter(a => a.userId === userId);

              usersWithDetails.push({
                ...user,
                assignments: userAssignments
              });
            }
          } catch (userError) {
            console.error(`Error fetching user ${userId}:`, userError);
            // Add user with basic info if fetch fails
            const userAssignments = allAssignedUsers.filter(a => a.userId === userId);
            usersWithDetails.push({
              id: userId,
              username: `User ${userId}`,
              email: 'N/A',
              assignments: userAssignments
            });
          }
        }

        setAssignedUsers(usersWithDetails);
      } catch (error) {
        console.error("Error fetching assigned users:", error);
        toast.error("Erreur lors du chargement des ressources affectées");
        setAssignedUsers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAssignedUsers();
  }, [planId]);

  // Filter users based on search query
  const filteredUsers = assignedUsers.filter(user =>
    user.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.assignments?.some(assignment =>
      assignment.role?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assignment.moduleName?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const getUserInitials = (user) => {
    if (user.username) {
      return user.username.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return user.email ? user.email[0].toUpperCase() : 'U';
  };

  const getRoleBadgeColor = (role) => {
    const roleColors = {
      'Chef de mission': 'bg-blue-100 text-blue-800',
      'Principal audité': 'bg-green-100 text-green-800',
      'Responsable': 'bg-purple-100 text-purple-800'
    };
    return roleColors[role] || 'bg-gray-100 text-gray-800';
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-[#F62D51]" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Affectation des Ressources</h2>
            <p className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <UserCheck className="h-4 w-4" />
          <span>{filteredUsers.length} utilisateur(s) affecté(s)</span>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher des utilisateurs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Users List */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
            <p className="text-gray-500">Chargement des ressources affectées...</p>
          </div>
        </div>
      ) : filteredUsers.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredUsers.map((user) => (
            <Card key={user.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={user.avatar} alt={user.username} />
                    <AvatarFallback className="bg-[#F62D51] text-white">
                      {getUserInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {user.username || 'Utilisateur inconnu'}
                    </h3>
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Mail className="h-3 w-3" />
                      <span className="truncate">{user.email || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {/* Contact Info */}
                  {(user.phone || user.location) && (
                    <div className="space-y-1">
                      {user.phone && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Phone className="h-3 w-3" />
                          <span>{user.phone}</span>
                        </div>
                      )}
                      {user.location && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="h-3 w-3" />
                          <span>{user.location}</span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Assignments */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                      <Briefcase className="h-3 w-3" />
                      <span>Affectations:</span>
                    </div>
                    <div className="space-y-2">
                      {user.assignments.map((assignment, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-2">
                          <div className="flex items-center justify-between mb-1">
                            <Badge className={getRoleBadgeColor(assignment.role)}>
                              {assignment.role}
                            </Badge>
                            <span className="text-xs text-gray-500">{assignment.module}</span>
                          </div>
                          <p className="text-sm text-gray-700 truncate" title={assignment.moduleName}>
                            {assignment.moduleName}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune ressource affectée
              </h3>
              <p className="text-gray-600">
                {searchQuery
                  ? 'Aucun utilisateur trouvé pour cette recherche'
                  : 'Aucun utilisateur n\'est actuellement affecté à ce plan d\'audit'
                }
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default AffectationRessourcesTab;
