import { useState, useEffect, useCallback, createContext, useContext } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import {
  FileText,
  Loader2,
  Activity,
  GitBranch,
  AlertCircle,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { toast } from "sonner";
import { getAuditConstatById } from '@/services/audit-constat-service';
import { getAuditPlanById } from '@/services/audit-plan-service';
import { getAuditActivityById } from '@/services/audit-activity-service';
import { getApiBaseUrl } from '@/utils/api-config';
import axios from 'axios';

export const OutletContext = createContext(null);
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

function EditConstats() {
  const { planId, missionAuditId, activiteId, constatId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  const [constat, setConstat] = useState(null);
  const [plan, setPlan] = useState(null);
  const [mission, setMission] = useState(null);
  const [activity, setActivity] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch constat data
  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch constat data
        const constatResponse = await getAuditConstatById(constatId, signal);
        if (!constatResponse.success) {
          throw new Error(constatResponse.message || 'Failed to fetch constat');
        }
        setConstat(constatResponse.data);

        // Fetch plan data if planId exists
        if (planId) {
          const planResponse = await getAuditPlanById(planId, signal);
          if (planResponse.success) {
            setPlan(planResponse.data);
          }
        }

        // Fetch mission data if missionAuditId exists
        if (missionAuditId) {
          try {
            const missionResponse = await axios.get(`${getApiBaseUrl()}/audit-missions/${missionAuditId}`, { signal });
            if (missionResponse.data.success) {
              setMission(missionResponse.data.data);
            }
          } catch (missionError) {
            if (!axios.isCancel(missionError)) {
              console.error("Error fetching mission:", missionError);
            }
          }
        }

        // Fetch activity data
        const activityResponse = await getAuditActivityById(activiteId, signal);
        if (activityResponse.success) {
          setActivity(activityResponse.data);
        }

      } catch (err) {
        if (axios.isCancel(err)) {
          console.log('Request cancelled:', err.message);
        } else {
          console.error('Error fetching data:', err);
          const errorMessage = err.response?.data?.message || err.message || 'An error occurred while fetching data';
          setError(errorMessage);
          toast.error(errorMessage);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (constatId) {
      fetchData();
    }

    return () => {
      abortController.abort();
    };
  }, [constatId, planId, activiteId]);

  // Refetch constat data function for child components
  const refetchConstat = useCallback(async () => {
    if (!constatId) return;

    setIsRefreshing(true);
    try {
      const constatResponse = await getAuditConstatById(constatId);
      if (constatResponse.success) {
        setConstat(constatResponse.data);
      } else {
        throw new Error(constatResponse.message || 'Failed to refresh constat');
      }
    } catch (err) {
      console.error('Error refreshing constat:', err);
      toast.error('Erreur lors de la mise à jour des données');
    } finally {
      setIsRefreshing(false);
    }
  }, [constatId]);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/fil-activite')) return 'fil-activite';
    if (path.includes('/workflows')) return 'workflows';
    return 'caracteristiques'; // Default tab
  }, [location.pathname]);

  // Define tabs
  const tabs = [
    { id: "caracteristiques", label: "Caractéristiques", icon: <FileText className="h-4 w-4" /> },
    { id: "fil-activite", label: "Fil d'activité", icon: <Activity className="h-4 w-4" /> },
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    let baseUrl = `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}`;
    switch (tabId) {
      case "caracteristiques":
        navigate(baseUrl);
        break;
      case "fil-activite":
        navigate(`${baseUrl}/fil-activite`);
        break;
      default:
        navigate(baseUrl);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Erreur</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!constat) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Constat non trouvé</h2>
          <p className="text-gray-600">Le constat demandé n'existe pas ou a été supprimé.</p>
        </div>
      </div>
    );
  }

  // Generate metadata from real data
  const metadata = [
    `Impact: ${constat.impact || 'N/A'}`,
    `Type: ${constat.type || 'N/A'}`,
    `Activité: ${activity?.name || 'N/A'}`
  ];

  // Get status badge details based on constat data
  const getStatusBadgeInfo = () => {
    // You can customize this based on your constat status field
    return { 
      label: constat.status || 'En cours', 
      variant: 'default', 
      color: 'bg-blue-100 text-blue-800' 
    };
  };

  // Breadcrumb with real data
  const breadcrumb = (
    <Breadcrumb>
      <BreadcrumbList className="text-sm">
        <BreadcrumbItem>
          <BreadcrumbLink
            href="/audit/plans-daudit"
            className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              navigate('/audit/plans-daudit');
            }}
          >
            Audit
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {planId && (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink
                href={`/audit/plans-daudit/${planId}`}
                className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                onClick={(e) => {
                  e.preventDefault();
                  navigate(`/audit/plans-daudit/${planId}`);
                }}
              >
                {plan?.name || `Plan ${planId}`}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
          </>
        )}
        <BreadcrumbItem>
          <BreadcrumbLink
            href={planId
              ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
              : `/audit/missions-audits/${missionAuditId}`
            }
            className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              const url = planId
                ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
                : `/audit/missions-audits/${missionAuditId}`;
              navigate(url);
            }}
          >
            {mission?.name || 'Mission d\'audit'}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbLink
            href={`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`}
            className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`);
            }}
          >
            {activity?.name || `Activité ${activiteId}`}
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        <BreadcrumbItem>
          <BreadcrumbPage className="text-gray-800 font-medium">{constat.name}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );

  // Tab bar
  const TabBar = () => (
    <div className="bg-white rounded-lg shadow-sm mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => navigateToTab(tab.id)}
              className={`
                px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center
                ${getCurrentTab() === tab.id
                  ? "border-[#F62D51] text-[#F62D51]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
              `}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );

  const handleGoBack = () => {
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`);
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      {/* Header with real constat information and breadcrumb */}
      <DetailHeader
        title={constat.name}
        icon={<FileText className="h-6 w-6 text-[#F62D51]" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des constats"
        breadcrumb={breadcrumb}
      />
      {/* Custom tab navigation */}
      <TabBar />
      {/* Tab content */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <Outlet context={{ constat, setConstat, refetchConstat, isRefreshing }} />
      </div>
    </div>
  );
}

export default EditConstats;
