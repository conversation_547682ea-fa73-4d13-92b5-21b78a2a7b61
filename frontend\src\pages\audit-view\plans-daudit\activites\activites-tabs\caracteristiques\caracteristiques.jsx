import { useState, useEffect, useRef, useCallback } from "react";
import { useOutletContext, useParams } from "react-router-dom";
import {
  Calendar,
  FileText,
  Save,
  ChevronUp,
  ChevronDown,
  ClipboardList,
  Loader2,
  User,
  Search,
  CheckCircle
} from "lucide-react";
import { debounce } from "lodash";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { DateInput } from "@/components/ui/date-input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";

function CaracteristiquesTab() {
  const { activite, refetchActivite, isRefreshing } = useOutletContext();
  const { missionAuditId, activiteId } = useParams();
  const API_BASE_URL = getApiBaseUrl();
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [missionData, setMissionData] = useState(null);
  const [users, setUsers] = useState([]);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const isMountedRef = useRef(true);
  const missionAbortControllerRef = useRef(null);
  const usersAbortControllerRef = useRef(null);
  const [formData, setFormData] = useState({
    nom: "",
    missionAudit: "",
    themeAudit: "",
    etat: "Non démarré",
    responsable: "",
    chargedetravailestimee: 0,
    chargedetravaileffective: 0,
    dateDebut: "",
    dateFin: "",
    objectif: "",
    progression: 0
  });

  // Add status translation mapping
  const statusTranslations = {
    'Not Started': 'Non démarré',
    'In Progress': 'En cours',
    'Completed': 'Terminé',
    'Delayed': 'Retardé',
    'Planned': 'Planifié',
    'Créé': 'Créé',
    'En cours': 'En cours',
    'Terminé': 'Terminé',
    'Retardé': 'Retardé',
    'Planifié': 'Planifié'
  };

  // Function to get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'En cours':
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Terminé':
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'Planifié':
      case 'Planned':
        return 'bg-yellow-100 text-yellow-800';
      case 'Retardé':
      case 'Delayed':
        return 'bg-red-100 text-red-800';
      case 'Non démarré':
      case 'Not Started':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Add function to convert ISO date to yyyy-MM-dd format
  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (missionAbortControllerRef.current) {
        missionAbortControllerRef.current.abort();
      }
      if (usersAbortControllerRef.current) {
        usersAbortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch mission data
  useEffect(() => {
    const fetchMissionData = async () => {
      // Don't fetch if component is unmounting
      if (!isMountedRef.current) return;

      // Cancel any existing request
      if (missionAbortControllerRef.current) {
        missionAbortControllerRef.current.abort();
      }

      // Create new abort controller
      missionAbortControllerRef.current = new AbortController();

      try {
        const response = await axios.get(
          `${API_BASE_URL}/audit-missions/${missionAuditId}`,
          { 
            withCredentials: true,
            signal: missionAbortControllerRef.current.signal,
            timeout: 10000
          }
        );

        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        if (response.data.success) {
          setMissionData(response.data.data);
        }
      } catch (error) {
        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        // Don't show error for aborted requests
        if (error.name === 'CanceledError' || error.code === 'ECONNABORTED') {
          console.log('Mission data request was aborted');
          return;
        }

        console.error("Error fetching mission data:", error);
        toast.error("Erreur lors du chargement des données de la mission");
      }
    };

    if (missionAuditId) {
      fetchMissionData();
    }

    return () => {
      if (missionAbortControllerRef.current) {
        missionAbortControllerRef.current.abort();
      }
    };
  }, [missionAuditId, API_BASE_URL]);

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      // Don't fetch if component is unmounting
      if (!isMountedRef.current) return;

      // Cancel any existing request
      if (usersAbortControllerRef.current) {
        usersAbortControllerRef.current.abort();
      }

      // Create new abort controller
      usersAbortControllerRef.current = new AbortController();

      try {
        setIsLoadingUsers(true);
        const response = await axios.get(
          `${API_BASE_URL}/users`,
          { 
            withCredentials: true,
            signal: usersAbortControllerRef.current.signal,
            timeout: 10000
          }
        );

        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        if (response.data.success) {
          setUsers(response.data.data);
        }
      } catch (error) {
        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        // Don't show error for aborted requests
        if (error.name === 'CanceledError' || error.code === 'ECONNABORTED') {
          console.log('Users request was aborted');
          return;
        }

        console.error("Error fetching users:", error);
        toast.error("Erreur lors du chargement des utilisateurs");
      } finally {
        if (isMountedRef.current) {
          setIsLoadingUsers(false);
        }
      }
    };

    fetchUsers();

    return () => {
      if (usersAbortControllerRef.current) {
        usersAbortControllerRef.current.abort();
      }
    };
  }, [API_BASE_URL]);

  // Initialize form data when activite is loaded
  useEffect(() => {
    if (activite && !isLoadingUsers) {
      console.log('=== DEBUG: Activity Data ===');
      console.log('Full activite:', activite);
      console.log('responsable:', activite.responsable, 'type:', typeof activite.responsable);
      console.log('Available users:', users);
      
      // Handle responsable assignment - only use responsable (integer)
      let responsableValue = '';
      let selectedUserData = null;
      
      // Only use responsable if it exists (this is the integer field from the database)
      if (activite.responsable !== null && activite.responsable !== undefined) {
        responsableValue = activite.responsable.toString();
        console.log('Using responsable:', responsableValue);
        
        // Find the user data for display from the users array
        const userData = users.find(user => 
          user.id === activite.responsable || 
          user.id === parseInt(activite.responsable) ||
          user.id.toString() === activite.responsable.toString()
        );
        if (userData) {
          selectedUserData = userData;
          console.log('Found user data:', selectedUserData);
        } else {
          console.log('No user found for ID:', activite.responsable);
          console.log('Available user IDs:', users.map(u => ({ id: u.id, type: typeof u.id })));
        }
      }
      
      console.log('Final responsableValue:', responsableValue, 'type:', typeof responsableValue);
      console.log('Selected user data:', selectedUserData);
      console.log('=== END DEBUG ===');
      
      setSelectedUser(selectedUserData);
      
      setFormData({
        nom: activite.nom || "",
        missionAudit: missionAuditId || "",
        themeAudit: activite.themeAudit || "",
        etat: statusTranslations[activite.statut] || "Non démarré",
        responsable: responsableValue,
        chargedetravailestimee: activite.chargedetravailestimee || 0,
        chargedetravaileffective: activite.chargedetravaileffective || 0,
        dateDebut: formatDateForInput(activite.dateDebut),
        dateFin: formatDateForInput(activite.dateFin),
        objectif: activite.objectif || "",
        progression: activite.progression || 0
      });
    }
  }, [activite, missionAuditId, users, isLoadingUsers]);

  // Debounced auto-save function
  const debouncedAutoSave = useCallback(
    debounce(async (currentFormData) => {
      if (!activiteId || !isMountedRef.current) {
        return;
      }

      // Validate required fields before saving
      if (!currentFormData.nom.trim()) {
        return; // Don't save if required fields are empty
      }

      setIsAutoSaving(true);
      try {
        const response = await axios.put(
          `${API_BASE_URL}/audit-activities/${activiteId}`,
          {
            name: currentFormData.nom,
            responsable: currentFormData.responsable || null,
            chargedetravailestimee: currentFormData.chargedetravailestimee,
            chargedetravaileffective: currentFormData.chargedetravaileffective,
            datedebut: currentFormData.dateDebut,
            datefin: currentFormData.dateFin,
            objectif: currentFormData.objectif,
            progression: currentFormData.progression,
            auditMissionID: missionAuditId
          },
          {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          }
        );

        if (response.data.success && isMountedRef.current) {
          toast.success("Sauvegarde automatique effectuée", {
            duration: 2000,
          });

          // Wait a moment before refreshing to ensure the backend has processed the update
          await new Promise(resolve => setTimeout(resolve, 100));

          // Then fetch fresh data
          await refetchActivite();
        } else if (isMountedRef.current) {
          throw new Error(response.data.message || "Erreur lors de la sauvegarde automatique");
        }
      } catch (error) {
        if (isMountedRef.current) {
          console.error("Error auto-saving activity:", error);
          toast.error("Erreur lors de la sauvegarde automatique");
        }
      } finally {
        if (isMountedRef.current) {
          setIsAutoSaving(false);
        }
      }
    }, 1500), // 1.5 second debounce delay
    [activiteId, missionAuditId, API_BASE_URL, refetchActivite]
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newFormData = {
        ...prev,
        [name]: value
      };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => {
      const newFormData = {
        ...prev,
        [name]: value
      };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  // Handle numeric input changes with validation
  const handleNumericInputChange = (e) => {
    const { name, value } = e.target;
    const numValue = parseInt(value);

    if (!isNaN(numValue) && numValue >= 0) {
      setFormData(prev => {
        const newFormData = {
          ...prev,
          [name]: numValue
        };
        // Trigger auto-save
        debouncedAutoSave(newFormData);
        return newFormData;
      });
    }
  };



  // Filter users based on search term
  const filteredUsers = users.filter(user => 
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle user selection
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    setFormData(prev => {
      const newFormData = { ...prev, responsable: user.id.toString() };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
    setIsUserModalOpen(false);
    setSearchTerm('');
  };

  // Handle clearing selected user
  const handleClearUser = () => {
    setSelectedUser(null);
    setFormData(prev => {
      const newFormData = { ...prev, responsable: '' };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  if (!activite) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des caractéristiques de l'activité...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <ClipboardList className="h-6 w-6 mr-3 text-[#F62D51]" />
          Caractéristiques de l'Activité
        </h2>
        <div className="flex items-center gap-2">
          {isAutoSaving && (
            <div className="flex items-center text-sm text-blue-600">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Sauvegarde automatique...
            </div>
          )}
          {isRefreshing && (
            <div className="flex items-center text-sm text-gray-600">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Mise à jour...
            </div>
          )}
        </div>
      </div>

      {/* Section Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen(!isCaracteristiquesOpen)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <FileText className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
        </button>

        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white">
            <div className="space-y-6">
              {/* Row 1: Nom (full width) */}
              <div className="w-full space-y-2">
                <Label htmlFor="nom">Nom *</Label>
                <Input
                  id="nom"
                  name="nom"
                  value={formData.nom}
                  onChange={handleInputChange}
                  placeholder="Nom de l'activité"
                  className="w-full"
                  required
                />
              </div>

              {/* Row 2: Mission d'audit, Thème d'audit, État */}
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="missionAudit">Mission d'audit</Label>
                  <div className="h-10 px-3 py-2 rounded-md border border-input bg-background text-sm flex items-center">
                    {missionData?.name || "Chargement..."}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="themeAudit">Thème d'audit</Label>
                  <Input
                    id="themeAudit"
                    name="themeAudit"
                    value={formData.themeAudit}
                    onChange={handleInputChange}
                    placeholder="Thème d'audit"
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="etat">État</Label>
                  <div className={`h-10 px-3 py-2 rounded-md border border-input bg-background text-sm flex items-center ${getStatusBadgeColor(formData.etat)}`}>
                    {formData.etat}
                  </div>
                </div>
              </div>

              {/* Row 3: Responsable (full width) */}
              <div className="w-full space-y-2">
                <Label htmlFor="responsable">Responsable</Label>
                <div className="flex gap-2">
                  <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        className="flex-1 justify-start"
                        type="button"
                        disabled={isLoadingUsers}
                      >
                        {isLoadingUsers ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                            <span>Chargement des utilisateurs...</span>
                          </div>
                        ) : selectedUser ? (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-green-600" />
                            <span className="font-medium">{selectedUser.username || selectedUser.email}</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-500">Aucun responsable assigné</span>
                          </div>
                        )}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>Sélectionner un responsable</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="relative">
                          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Rechercher un utilisateur..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                        <div className="max-h-60 overflow-y-auto space-y-2">
                          {isLoadingUsers ? (
                            <div className="flex items-center justify-center py-8">
                              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                              <span className="ml-2 text-gray-500">Chargement des utilisateurs...</span>
                            </div>
                          ) : filteredUsers.length > 0 ? (
                            filteredUsers.map(user => (
                              <div
                                key={user.id}
                                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                                onClick={() => handleUserSelect(user)}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <User className="h-4 w-4 text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="font-medium">{user.username}</p>
                                    <p className="text-sm text-gray-500">{user.email}</p>
                                  </div>
                                </div>
                                {selectedUser?.id === user.id && (
                                  <CheckCircle className="h-5 w-5 text-green-500" />
                                )}
                              </div>
                            ))
                          ) : (
                            <p className="text-center text-gray-500 py-4">
                              {searchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                            </p>
                          )}
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                  {selectedUser && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearUser}
                      type="button"
                      className="px-3"
                    >
                      <span className="text-red-600">×</span>
                    </Button>
                  )}
                </div>
              </div>

              {/* Row 4: Charge de travail estimée, Charge de travail effective */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="chargedetravailestimee">Charge de travail estimée (heures)</Label>
                  <Input
                    id="chargedetravailestimee"
                    name="chargedetravailestimee"
                    type="number"
                    min="0"
                    value={formData.chargedetravailestimee}
                    onChange={handleNumericInputChange}
                    placeholder="Heures estimées"
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="chargedetravaileffective">Charge de travail effective (heures)</Label>
                  <Input
                    id="chargedetravaileffective"
                    name="chargedetravaileffective"
                    type="number"
                    min="0"
                    value={formData.chargedetravaileffective}
                    onChange={handleNumericInputChange}
                    placeholder="Heures effectives"
                    className="w-full"
                  />
                </div>
              </div>

              {/* Row 5: Date de début, Date de fin */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dateDebut" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    Date de début
                  </Label>
                  <DateInput
                    id="dateDebut"
                    name="dateDebut"
                    value={formData.dateDebut}
                    onChange={handleInputChange}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dateFin" className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    Date de fin
                  </Label>
                  <DateInput
                    id="dateFin"
                    name="dateFin"
                    value={formData.dateFin}
                    onChange={handleInputChange}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Row 6: Objectif de contrôle (full width) */}
              <div className="w-full space-y-2">
                <Label htmlFor="objectif">Objectif de contrôle</Label>
                <Textarea
                  id="objectif"
                  name="objectif"
                  value={formData.objectif}
                  onChange={handleInputChange}
                  placeholder="Objectif de contrôle de l'activité"
                  rows={3}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CaracteristiquesTab;
