import React, { useState, useEffect, useCallback, createContext, useContext, useRef } from "react";
import { useParams, useNavigate, useLocation, Outlet } from "react-router-dom";
import {
  FileText,
  Loader2,
  Activity,
  ClipboardList,
  ArrowLeft,
  GitBranch,
  ListChecks,
  LayoutDashboard
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { toast } from "sonner";
import { getAuditPlanById } from "@/services/audit-plan-service";
import { getAuditMissionById } from "@/services/audit-mission-service";
import { getAuditActivityById } from "@/services/audit-activity-service";
import { getAuditConstatById } from "@/services/audit-constat-service";
import { getRecommendationById } from "@/services/audit-recommendation-service";

// Create context for recommendation data
export const OutletContext = createContext(null);
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

function EditRecommandation() {
  const { planId, missionAuditId, activiteId, constatId, recommandationId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const abortControllerRef = useRef(null);

  const [plan, setPlan] = useState(null);
  const [mission, setMission] = useState(null);
  const [activity, setActivity] = useState(null);
  const [constat, setConstat] = useState(null);
  const [recommandation, setRecommandation] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      // Cancel any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new AbortController
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch all required data in parallel
        const [planData, missionData, activityData, constatData, recommendationData] = await Promise.all([
          getAuditPlanById(planId, signal),
          getAuditMissionById(missionAuditId, signal),
          getAuditActivityById(activiteId, signal),
          getAuditConstatById(constatId, signal),
          getRecommendationById(recommandationId, signal)
        ]);

        if (!planData?.success || !missionData?.success || !activityData?.success || 
            !constatData?.success || !recommendationData?.success) {
          throw new Error('Failed to fetch one or more required resources');
        }

        setPlan(planData.data);
        setMission(missionData.data);
        setActivity(activityData.data);
        setConstat(constatData.data);
        setRecommandation(recommendationData.data);
      } catch (error) {
        if (error.name === 'CanceledError') {
          console.log('Request cancelled:', error.message);
          return;
        }
        console.error('Error fetching data:', error);
        setError(error.message || 'Une erreur est survenue lors du chargement des données');
        toast.error('Erreur lors du chargement des données');
      } finally {
        setIsLoading(false);
      }
    };

    if (planId && missionAuditId && activiteId && constatId && recommandationId) {
      fetchData();
    }

    // Cleanup
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [planId, missionAuditId, activiteId, constatId, recommandationId]);

  // Refetch recommendation data function for child components
  const refetchRecommandation = useCallback(async () => {
    if (!recommandationId) return;

    setIsRefreshing(true);
    try {
      const response = await getRecommendationById(recommandationId);
      if (response.success) {
        setRecommandation(response.data);
      } else {
        throw new Error(response.message || 'Failed to refresh recommendation');
      }
    } catch (err) {
      console.error('Error refreshing recommendation:', err);
      toast.error('Erreur lors de la mise à jour des données');
    } finally {
      setIsRefreshing(false);
    }
  }, [recommandationId]);

  // Define tabs with new order
  const tabs = [
    { id: "vue-ensemble", label: "Vue d'ensemble", icon: <LayoutDashboard className="h-4 w-4" /> },
    { id: "caracteristiques", label: "Caractéristiques", icon: <FileText className="h-4 w-4" /> },
    { id: "plan-action", label: "Plan d'action", icon: <ListChecks className="h-4 w-4" /> },
    { id: "fil-activite", label: "Fil d'activité", icon: <Activity className="h-4 w-4" /> },
    { id: "workflow", label: "Workflow", icon: <GitBranch className="h-4 w-4" /> },
  ];

  // Update getCurrentTab to match new order
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (!path.includes('/vue-ensemble') && 
        !path.includes('/plan-action') && 
        !path.includes('/fil-activite') && 
        !path.includes('/workflow')) {
      return 'caracteristiques';
    }
    if (path.includes('/vue-ensemble')) return 'vue-ensemble';
    if (path.includes('/plan-action')) return 'plan-action';
    if (path.includes('/fil-activite')) return 'fil-activite';
    if (path.includes('/workflow')) return 'workflow';
    return 'vue-ensemble';
  }, [location.pathname]);

  // Update navigateToTab to match new order
  const navigateToTab = (tabId) => {
    const baseUrl = `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}/recommandations/${recommandationId}`;
    switch (tabId) {
      case "vue-ensemble":
        navigate(`${baseUrl}/vue-ensemble`);
        break;
      case "caracteristiques":
        navigate(baseUrl);
        break;
      case "plan-action":
        navigate(`${baseUrl}/plan-action`);
        break;
      case "fil-activite":
        navigate(`${baseUrl}/fil-activite`);
        break;
      case "workflow":
        navigate(`${baseUrl}/workflow`);
        break;
      default:
        navigate(`${baseUrl}/vue-ensemble`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Erreur</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!recommandation || !plan || !mission || !activity || !constat) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Ressource non trouvée</h2>
          <p className="text-gray-600">Une ou plusieurs ressources demandées n'existent pas ou ont été supprimées.</p>
        </div>
      </div>
    );
  }

  // Generate metadata
  const metadata = [
    `Priorité: ${recommandation.priorite || 'N/A'}`,
    `Responsable: ${recommandation.responsableId ? recommandation.responsableUser?.username || 'N/A' : 'N/A'}`,
    `Date de création: ${recommandation.createdAt ? new Date(recommandation.createdAt).toLocaleDateString('fr-FR') : 'N/A'}`,
    `Dernière mise à jour: ${recommandation.updatedAt ? new Date(recommandation.updatedAt).toLocaleDateString('fr-FR') : 'N/A'}`
  ];

  // Get status badge details
  const getStatusBadgeInfo = () => {
    // You can implement your own status logic here
        return { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' };
  };

  const handleGoBack = () => {
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}`);
  };

  // Create context value
  const contextValue = {
    recommandation,
    setRecommandation,
    refetchRecommandation,
    isRefreshing,
    plan,
    mission,
    activity,
    constat,
    planId,
    missionAuditId,
    activiteId,
    constatId
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      {/* Header with recommendation information and breadcrumb */}
      <DetailHeader
        title={recommandation.name}
        icon={<FileText className="h-6 w-6 text-[#F62D51]" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des recommandations"
        breadcrumb={
          <Breadcrumb>
            <BreadcrumbList className="text-sm">
              <BreadcrumbItem>
                <BreadcrumbLink
                  href="/audit/plans-daudit"
                  className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/audit/plans-daudit');
                  }}
                >
                  Audit
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={`/audit/plans-daudit/${planId}`}
                  className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/audit/plans-daudit/${planId}`);
                  }}
                >
                  {plan.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`}
                  className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`);
                  }}
                >
                  {mission.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`}
                  className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`);
                  }}
                >
                  {activity.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}`}
                  className="text-gray-500 font-medium hover:text-blue-600 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constatId}`);
                  }}
                >
                  {constat.name}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-gray-800 font-medium">{recommandation.name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        }
      />

      <OutletContext.Provider value={contextValue}>
        {/* Custom tab navigation */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => navigateToTab(tab.id)}
                  className={`
                    px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center
                    ${getCurrentTab() === tab.id
                      ? "border-[#F62D51] text-[#F62D51]"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
                  `}
                >
                  {tab.icon}
                  <span className="ml-2">{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab content */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <Outlet context={contextValue} />
        </div>
      </OutletContext.Provider>
    </div>
  );
}

export default EditRecommandation;
