import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import TablePagination from "@/components/ui/table-pagination";
import { Plus, Search, ArrowUpDown, Edit, Trash2, Filter as FilterIcon, XCircle, Loader2, AlertCircle, ChevronUp } from "lucide-react";
import { toast } from 'sonner';
import { Textarea } from "@/components/ui/textarea";
import missionIcon from '@/assets/mission.png';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

function MissionsAuditsList() {
  const navigate = useNavigate();
  const { id: auditPlanId } = useParams();
  const [auditMissions, setAuditMissions] = useState([]);
  const [auditPlan, setAuditPlan] = useState(null);
  const [users, setUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMissions, setSelectedMissions] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: 'datedebut', direction: 'desc' });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [filters] = useState({
    status: 'all',
    category: 'all'
  });

  const [newMission, setNewMission] = useState({
    name: "",
    categorie: "",
    code: "",
    etat: "Planifié",
    chefmission: null,
    principalAudite: "",
    objectif: "",
    avancement: 0,
    planifieInitialement: false,
    evaluation: null,
    datedebut: "",
    datefin: "",
    pointfort: "",
    pointfaible: "",
    auditplanID: auditPlanId
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Preload icon
  const [iconLoaded, setIconLoaded] = useState(false);

  useEffect(() => {
    if (missionIcon) {
      const img = new Image();
      img.src = missionIcon;
      img.onload = () => setIconLoaded(true);
    }
  }, []);

  // Fetch audit plan, missions, and users
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch audit plan, missions, and users in parallel
        const [planResponse, missionsResponse, usersResponse] = await Promise.all([
          axios.get(`${getApiBaseUrl()}/audit-plans/${auditPlanId}`),
          axios.get(`${getApiBaseUrl()}/audit-missions/plan/${auditPlanId}`),
          axios.get(`${getApiBaseUrl()}/users`)
        ]);

        if (planResponse.data.success) {
          setAuditPlan(planResponse.data.data);
        }

        if (missionsResponse.data.success) {
          setAuditMissions(missionsResponse.data.data);
        } else {
          toast.error("Erreur lors du chargement des missions");
        }

        if (usersResponse.data.success) {
          setUsers(usersResponse.data.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error("Erreur lors du chargement des données");
      } finally {
        setIsLoading(false);
      }
    };

    if (auditPlanId) {
      fetchData();
    }
  }, [auditPlanId]);

  const filteredMissions = useMemo(() => {
    return auditMissions.filter(mission => {
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          mission.name.toLowerCase().includes(query) ||
          (mission.categorie && mission.categorie.toLowerCase().includes(query)) ||
          (mission.code && mission.code.toLowerCase().includes(query));
        if (!matchesSearch) return false;
      }
      if (filters.status !== 'all' && mission.etat !== filters.status) return false;
      if (filters.category !== 'all' && mission.categorie !== filters.category) return false;
      return true;
    });
  }, [auditMissions, searchQuery, filters]);

  const sortedMissions = useMemo(() => {
    return [...filteredMissions].sort((a, b) => {
      if (!sortConfig.key) return 0;
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];
      if (sortConfig.key === 'datedebut' || sortConfig.key === 'datefin') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }
      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredMissions, sortConfig]);

  const currentMissions = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedMissions.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedMissions, currentPage, itemsPerPage]);

  if (!auditPlanId) {
    return (
      <div className="flex items-center justify-center h-full py-10">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500 mr-3" />
        <p className="text-gray-500">Chargement des missions du plan d'audit...</p>
      </div>
    );
  }

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    setSelectedMissions(checked ? currentMissions.map(m => m.id) : []);
  };

  const handleSelectMission = (id) => {
    setSelectedMissions(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const handleRowClick = (missionId) => {
    navigate(`/audit/plans-daudit/${auditPlanId}/missions-audits/${missionId}`);
  };

  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewMission(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateSelectChange = (name, value) => {
    setNewMission(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCreateMission = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!newMission.name || !newMission.datedebut || !newMission.datefin) {
        throw new Error("Veuillez remplir tous les champs obligatoires: Nom, Date de début et Date de fin.");
      }

      const missionData = {
        ...newMission,
        chefmission: newMission.chefmission || null,
        evaluation: newMission.evaluation || null,
        categorie: newMission.categorie || null,
        code: newMission.code || null,
        principalAudite: newMission.principalAudite || null,
        objectif: newMission.objectif || null,
        pointfort: newMission.pointfort || null,
        pointfaible: newMission.pointfaible || null
      };

      const response = await axios.post(`${getApiBaseUrl()}/audit-missions`, missionData);
      
      if (response.data.success) {
        setAuditMissions(prev => [response.data.data, ...prev]);
        toast.success("Nouvelle mission d'audit créée avec succès!");
        setNewMission({
          name: "",
          categorie: "",
          code: "",
          etat: "Planifié",
          chefmission: null,
          principalAudite: "",
          objectif: "",
          avancement: 0,
          planifieInitialement: false,
          evaluation: null,
          datedebut: "",
          datefin: "",
          pointfort: "",
          pointfaible: "",
          auditplanID: auditPlanId
        });
        setIsCreateModalOpen(false);
      } else {
        throw new Error(response.data.message || "Erreur lors de la création de la mission");
      }
    } catch (error) {
      console.error('Error creating mission:', error);
      toast.error(error?.response?.data?.message || error?.message || "Échec de la création de la mission");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedMissions.length === 0) {
      toast.error("Aucune mission sélectionnée");
      return;
    }

    if (window.confirm(`Êtes-vous sûr de vouloir supprimer ${selectedMissions.length} mission(s) sélectionnée(s) ?`)) {
      try {
        setIsSubmitting(true);
        const deletePromises = selectedMissions.map(id => 
          axios.delete(`${getApiBaseUrl()}/audit-missions/${id}`)
        );
        
        await Promise.all(deletePromises);
        
        setAuditMissions(prev => prev.filter(mission => !selectedMissions.includes(mission.id)));
        setSelectedMissions([]);
        toast.success(`${selectedMissions.length} mission(s) d'audit supprimée(s) avec succès.`);
      } catch (error) {
        console.error('Error in bulk delete operation:', error);
        toast.error(error?.response?.data?.message || 'Une erreur inattendue est survenue lors de la suppression');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'Planifié': { variant: 'default', label: 'Planifié' },
      'En cours': { variant: 'warning', label: 'En cours' },
      'Terminé': { variant: 'success', label: 'Terminé' },
      'Retardé': { variant: 'destructive', label: 'Retardé' },
      'Annulé': { variant: 'secondary', label: 'Annulé' }
    };

    const config = statusConfig[status] || { variant: 'default', label: status || 'N/A' };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  // Helper function to get user name by ID
  const getUserName = (userId) => {
    if (!userId) return '-';

    // If userId is already a string (name), return it
    if (typeof userId === 'string' && isNaN(parseInt(userId))) {
      return userId;
    }

    // If userId is a number or numeric string, find the user
    const user = users.find(u => u.id === parseInt(userId));
    return user ? (user.username || user.email) : `ID: ${userId}`;
  };

  const columns = [
    { key: 'name', label: 'Nom', sortable: true },
    { key: 'categorie', label: 'Catégorie', sortable: true },
    { key: 'etat', label: 'Statut', sortable: true },
    { key: 'avancement', label: 'Avancement (%)', sortable: true },
    { key: 'datedebut', label: 'Date début', sortable: true },
    { key: 'datefin', label: 'Date fin', sortable: true },
    { key: 'chefmission', label: 'Chef de mission', sortable: true },
    { key: 'principalAudite', label: 'Principal audité', sortable: true }
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full py-10">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500 mr-3" />
        <p className="text-gray-500">Chargement des missions...</p>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 space-y-4">
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
          <h2 className="text-xl font-bold text-gray-900">Missions du Plan d'Audit: {auditPlan?.name || 'Chargement...'}</h2>
          <div className="w-full sm:w-auto sm:max-w-xs">
            <div className="relative">
              <Input
                placeholder="Rechercher des missions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-9 w-full pr-8"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedMissions.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteSelected}
            disabled={isSubmitting}
            className="border-red-500 text-red-500 hover:bg-red-50 flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Supprimer ({selectedMissions.length})
          </Button>
        )}
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white flex items-center gap-2">
              <Plus className="h-4 w-4" /> Nouvelle Mission
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Créer une Nouvelle Mission</DialogTitle>
              <DialogDescription>Remplissez les champs ci-dessous.</DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateMission} className="space-y-4 pt-4">
              <div className="space-y-1">
                <Label htmlFor="name">Nom *</Label>
                <Input id="name" name="name" value={newMission.name} onChange={handleCreateInputChange} required />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label htmlFor="categorie">Catégorie</Label>
                  <Select
                    name="categorie"
                    value={newMission.categorie}
                    onValueChange={(value) => setNewMission(prev => ({ ...prev, categorie: value }))}
                  >
                    <SelectTrigger id="categorie" className="w-full">
                      <SelectValue placeholder="Sélectionner une catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Conformité">Conformité</SelectItem>
                      <SelectItem value="Efficacité">Efficacité</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1">
                  <Label htmlFor="code">Code</Label>
                  <Input id="code" name="code" value={newMission.code} onChange={handleCreateInputChange} />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label htmlFor="datedebut">Date début *</Label>
                  <Input id="datedebut" name="datedebut" type="date" value={newMission.datedebut} onChange={handleCreateInputChange} required />
                </div>
                <div className="space-y-1">
                  <Label htmlFor="datefin">Date fin *</Label>
                  <Input id="datefin" name="datefin" type="date" value={newMission.datefin} onChange={handleCreateInputChange} required />
                </div>
              </div>
              <div className="space-y-1">
                <Label htmlFor="etat">Statut</Label>
                <Select name="etat" value={newMission.etat} onValueChange={(value) => handleCreateSelectChange('etat', value)}>
                  <SelectTrigger><SelectValue /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Planifié">Planifié</SelectItem>
                    <SelectItem value="En cours">En cours</SelectItem>
                    <SelectItem value="Terminé">Terminé</SelectItem>
                    <SelectItem value="Retardé">Retardé</SelectItem>
                    <SelectItem value="Annulé">Annulé</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <Label htmlFor="objectif">Objectif</Label>
                <Textarea id="objectif" name="objectif" value={newMission.objectif} onChange={handleCreateInputChange} rows={2} />
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)} disabled={isSubmitting}>Annuler</Button>
                <Button type="submit" className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white" disabled={isSubmitting}>
                  {isSubmitting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Création...</> : "Créer Mission"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="w-[50px] px-4 py-3">
                    <Checkbox checked={currentMissions.length > 0 && selectedMissions.length === currentMissions.length} onCheckedChange={handleSelectAll} disabled={currentMissions.length === 0} aria-label="Select all missions" />
                  </TableHead>
                  {columns.map((column) => (
                    <TableHead key={column.key} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => column.sortable && handleSort(column.key)}>
                      <div className="flex items-center gap-1">{column.label}{column.sortable && sortConfig.key === column.key && (sortConfig.direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ArrowUpDown className="w-3 h-3" />)}</div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {currentMissions.length > 0 ? (
                  currentMissions.map((mission) => (
                    <TableRow key={mission.id} className="hover:bg-gray-50/50 cursor-pointer" onClick={() => handleRowClick(mission.id)}>
                      <TableCell className="px-4 py-3">
                        <Checkbox checked={selectedMissions.includes(mission.id)} onCheckedChange={(checked) => { checked ? handleSelectMission(mission.id) : handleSelectMission(mission.id) }} onClick={(e) => e.stopPropagation()} aria-label={`Select mission ${mission.name}`} />
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          {missionIcon && iconLoaded ? (
                            <img
                              src={missionIcon}
                              alt="mission icon"
                              className="w-5 h-5 object-contain"
                              onError={(e) => {
                                e.target.src = 'https://placehold.co/20x20/cccccc/969696?text=A';
                                e.target.onerror = null;
                              }}
                            />
                          ) : (
                            <div className="w-5 h-5 bg-[#F62D51] rounded-full flex items-center justify-center text-white text-xs">M</div>
                          )}
                          {mission.name}
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{mission.categorie || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{getStatusBadge(mission.etat)}</TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{mission.avancement || 0}%</TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{new Date(mission.datedebut).toLocaleDateString('fr-FR')}</TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{new Date(mission.datefin).toLocaleDateString('fr-FR')}</TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{getUserName(mission.chefmission)}</TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{getUserName(mission.principalAudite)}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length + 1} className="px-4 py-8 text-center text-gray-500">
                      Aucune mission trouvée
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <TablePagination
        currentPage={currentPage}
        totalItems={filteredMissions.length}
        itemsPerPage={itemsPerPage}
        onPageChange={setCurrentPage}
        onItemsPerPageChange={setItemsPerPage}
      />
    </div>
  );
}

export default MissionsAuditsList;
