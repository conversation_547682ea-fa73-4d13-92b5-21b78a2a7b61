'use strict';

const db = require('../../models');
const AuditRecommendation = db.AuditRecommendation;
const AuditConstat = db.AuditConstat;
const ActionPlan = db.ActionPlan;
const ActionPlanAttachment = db.ActionPlanAttachment;
const AuditActivity = db.AuditActivity;
const AuditMission = db.AuditMission;
const { v4: uuidv4 } = require('uuid');

// Get all recommendations
const getAllRecommendations = async (req, res) => {
  try {
    const recommendations = await AuditRecommendation.findAll({
      include: [
        {
          model: ActionPlan,
          as: 'actionPlan',
          include: [{ model: ActionPlanAttachment, as: 'attachments' }]
        },
        { model: AuditConstat, as: 'constats' }
      ]
    });
    return res.status(200).json({ success: true, data: recommendations });
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch recommendations', error: error.message });
  }
};

// Get recommendations by constat ID
const getRecommendationsByConstatId = async (req, res) => {
  try {
    const { constatId } = req.params;
    const recommendations = await AuditRecommendation.findAll({
      include: [
        {
          model: AuditConstat,
          as: 'constats',
          where: { id: constatId }
        },
        {
          model: ActionPlan,
          as: 'actionPlan',
          include: [{ model: ActionPlanAttachment, as: 'attachments' }]
        }
      ]
    });
    return res.status(200).json({ success: true, data: recommendations });
  } catch (error) {
    console.error('Error fetching recommendations by constat ID:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch recommendations', error: error.message });
  }
};

// Create a new recommendation
const createRecommendation = async (req, res) => {
  try {
    const {
      name,
      priorite,
      description,
      planification,
      code,
      details,
      constatIds // Array of constat IDs
    } = req.body;

    // Validate required fields
    if (!name) return res.status(400).json({ success: false, message: 'Name is required' });
    if (!constatIds || !Array.isArray(constatIds) || constatIds.length === 0) {
      return res.status(400).json({ success: false, message: 'At least one constat ID is required' });
    }

    // Fetch associated constats
    const constats = await AuditConstat.findAll({ where: { id: constatIds } });
    if (constats.length !== constatIds.length) {
      return res.status(404).json({ success: false, message: 'One or more constats not found' });
    }

    // Use responsableId from the first constat if available, otherwise null
    const responsableId = constats[0]?.responsable || null;

    // Create action plan
    const actionPlan = await ActionPlan.create({
      actionPlanID: `AP_${uuidv4().substring(0, 8)}`,
      name,
      priority: priorite
    });

    // Create action plan attachment
    const attachment = await ActionPlanAttachment.create({
      attachmentID: `APA_${uuidv4().substring(0, 8)}`,
      fileName: name,
      uploadDate: new Date(),
      type: 'business-document',
      actionPlanID: actionPlan.actionPlanID
    });

    // Create recommendation
    const recommendation = await AuditRecommendation.create({
      id: `REC_${uuidv4().substring(0, 8)}`,
      name,
      priorite,
      description,
      planification,
      code,
      details,
      responsableId,
      actionPlanID: actionPlan.actionPlanID
    });

    // Associate with constats
    await recommendation.setConstats(constats);

    // Fetch the created recommendation with associations
    const createdRecommendation = await AuditRecommendation.findByPk(recommendation.id, {
      include: [
        { model: ActionPlan, as: 'actionPlan', include: [{ model: ActionPlanAttachment, as: 'attachments' }] },
        { model: AuditConstat, as: 'constats' }
      ]
    });

    return res.status(201).json({ success: true, data: createdRecommendation });
  } catch (error) {
    console.error('Error creating recommendation:', error);
    return res.status(500).json({ success: false, message: 'Failed to create recommendation', error: error.message });
  }
};

// Get recommendation by ID
const getRecommendationById = async (req, res) => {
  try {
    const { id } = req.params;
    const recommendation = await AuditRecommendation.findByPk(id, {
      include: [
        {
          model: ActionPlan,
          as: 'actionPlan',
          include: [{ model: ActionPlanAttachment, as: 'attachments' }]
        },
        { model: AuditConstat, as: 'constats' }
      ]
    });
    if (!recommendation) return res.status(404).json({ success: false, message: 'Recommendation not found' });
    return res.status(200).json({ success: true, data: recommendation });
  } catch (error) {
    console.error('Error fetching recommendation:', error);
    return res.status(500).json({ success: false, message: 'Failed to fetch recommendation', error: error.message });
  }
};

// Update recommendation
const updateRecommendation = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, priorite, description, planification, code, details, constatIds, linkToConstat } = req.body;

    const recommendation = await AuditRecommendation.findByPk(id);
    if (!recommendation) return res.status(404).json({ success: false, message: 'Recommendation not found' });

    // Handle linking to a new constat
    if (linkToConstat) {
      const constat = await AuditConstat.findByPk(linkToConstat);
      if (!constat) {
        return res.status(404).json({ success: false, message: 'Constat not found' });
      }
      await recommendation.addConstat(constat);
    }
    // Handle updating all constat associations (used when editing the recommendation)
    else if (constatIds && Array.isArray(constatIds)) {
      const constats = await AuditConstat.findAll({ where: { id: constatIds } });
      if (constats.length !== constatIds.length) {
        return res.status(404).json({ success: false, message: 'One or more constats not found' });
      }
      await recommendation.setConstats(constats);
    }

    // Update recommendation fields
    await recommendation.update({
      name: name || recommendation.name,
      priorite: priorite !== undefined ? priorite : recommendation.priorite,
      description: description !== undefined ? description : recommendation.description,
      planification: planification !== undefined ? planification : recommendation.planification,
      code: code !== undefined ? code : recommendation.code,
      details: details !== undefined ? details : recommendation.details
    });

    // Fetch the updated recommendation with associations
    const updatedRecommendation = await AuditRecommendation.findByPk(id, {
      include: [
        { model: ActionPlan, as: 'actionPlan', include: [{ model: ActionPlanAttachment, as: 'attachments' }] },
        { model: AuditConstat, as: 'constats' }
      ]
    });

    return res.status(200).json({ success: true, message: 'Recommendation updated successfully', data: updatedRecommendation });
  } catch (error) {
    console.error('Error updating recommendation:', error);
    return res.status(500).json({ success: false, message: 'Failed to update recommendation', error: error.message });
  }
};

// Delete recommendation
const deleteRecommendation = async (req, res) => {
  try {
    const { id } = req.params;
    const recommendation = await AuditRecommendation.findByPk(id);
    if (!recommendation) return res.status(404).json({ success: false, message: 'Recommendation not found' });

    await recommendation.destroy();
    return res.status(200).json({ success: true, message: 'Recommendation deleted successfully' });
  } catch (error) {
    console.error('Error deleting recommendation:', error);
    return res.status(500).json({ success: false, message: 'Failed to delete recommendation', error: error.message });
  }
};

// Get recommendation by actionPlanID
const getRecommendationByActionPlanId = async (req, res) => {
  try {
    const { actionPlanId } = req.params;

    const recommendation = await AuditRecommendation.findOne({
      where: { actionPlanID: actionPlanId },
      include: [
        {
          model: AuditConstat,
          as: 'constats',
          include: [
            {
              model: AuditActivity,
              as: 'auditActivity',
              include: [
                {
                  model: AuditMission,
                  as: 'auditMission',
                  include: [
                    {
                      model: db.AuditPlan,
                      as: 'auditPlan'
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    if (!recommendation) {
      return res.status(404).json({
        success: false,
        message: 'Recommendation not found for this action plan'
      });
    }

    // Format the response with the required fields
    const formattedResponse = {
      recommendation: {
        id: recommendation.id,
        name: recommendation.name
      },
      constats: recommendation.constats.map(constat => ({
        id: constat.id,
        name: constat.name,
        activity: constat.auditActivity ? {
          id: constat.auditActivity.id,
          name: constat.auditActivity.name,
          mission: constat.auditActivity.auditMission ? {
            id: constat.auditActivity.auditMission.id,
            name: constat.auditActivity.auditMission.name,
            plan: constat.auditActivity.auditMission.auditPlan ? {
              id: constat.auditActivity.auditMission.auditPlan.id,
              name: constat.auditActivity.auditMission.auditPlan.name
            } : null
          } : null
        } : null
      }))
    };

    return res.status(200).json({ success: true, data: formattedResponse });
  } catch (error) {
    console.error('Error fetching recommendation by action plan ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch recommendation',
      error: error.message
    });
  }
};

// Get recommendations by audit plan ID
const getRecommendationsByAuditPlanId = async (req, res) => {
  try {
    const { planId } = req.params;

    // Find all recommendations that have constats linked to activities in missions of this audit plan
    const recommendations = await AuditRecommendation.findAll({
      include: [
        {
          model: AuditConstat,
          as: 'constats',
          required: true, // Only include recommendations that have constats
          include: [
            {
              model: AuditActivity,
              as: 'auditActivity',
              required: true,
              include: [
                {
                  model: AuditMission,
                  as: 'auditMission',
                  required: true,
                  where: { planId: planId }, // Filter by audit plan ID
                  include: [
                    {
                      model: db.AuditPlan,
                      as: 'auditPlan'
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    });

    if (!recommendations || recommendations.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No recommendations found for this audit plan'
      });
    }

    // Format the response - return all recommendations with their hierarchy
    const formattedRecommendations = recommendations.map(recommendation => ({
      recommendation: {
        id: recommendation.id,
        name: recommendation.name,
        priorite: recommendation.priorite,
        description: recommendation.description,
        planification: recommendation.planification,
        code: recommendation.code,
        details: recommendation.details
      },
      constats: recommendation.constats.map(constat => ({
        id: constat.id,
        name: constat.name,
        activity: constat.auditActivity ? {
          id: constat.auditActivity.id,
          name: constat.auditActivity.name,
          mission: constat.auditActivity.auditMission ? {
            id: constat.auditActivity.auditMission.id,
            name: constat.auditActivity.auditMission.name,
            plan: constat.auditActivity.auditMission.auditPlan ? {
              id: constat.auditActivity.auditMission.auditPlan.id,
              name: constat.auditActivity.auditMission.auditPlan.name
            } : null
          } : null
        } : null
      }))
    }));

    return res.status(200).json({ success: true, data: formattedRecommendations });
  } catch (error) {
    console.error('Error fetching recommendations by audit plan ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch recommendations',
      error: error.message
    });
  }
};

module.exports = {
  getAllRecommendations,
  getRecommendationsByConstatId,
  createRecommendation,
  getRecommendationById,
  updateRecommendation,
  deleteRecommendation,
  getRecommendationByActionPlanId,
  getRecommendationsByAuditPlanId
};
