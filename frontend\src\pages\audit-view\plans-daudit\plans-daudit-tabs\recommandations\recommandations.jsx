import { useOutletContext, useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { ListChecks, Search, Eye, Edit, Trash2, Plus, Loader2, AlertCircle } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import TablePagination from "@/components/ui/table-pagination";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";

function RecommandationsTab() {
  const { auditPlan } = useOutletContext();
  const { id: planId } = useParams();

  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Fetch recommendations for this specific plan
  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!planId) return;

      try {
        setLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/recommendations/plan/${planId}`, {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.data.success) {
          setRecommendations(response.data.data || []);
        } else {
          toast.error("Erreur lors du chargement des recommandations");
        }
      } catch (error) {
        console.error("Error fetching recommendations:", error);
        toast.error("Erreur lors du chargement des recommandations");
        setRecommendations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [planId]);

  // Filter recommendations based on search query
  const filteredRecommendations = recommendations.filter(rec =>
    rec.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.status?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredRecommendations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredRecommendations.length);
  const currentRecommendations = filteredRecommendations.slice(startIndex, endIndex);

  const getStatusBadge = (status) => {
    const statusConfig = {
      'En cours': { color: 'bg-blue-100 text-blue-800', label: 'En cours' },
      'Terminé': { color: 'bg-green-100 text-green-800', label: 'Terminé' },
      'En attente': { color: 'bg-yellow-100 text-yellow-800', label: 'En attente' },
      'Annulé': { color: 'bg-red-100 text-red-800', label: 'Annulé' }
    };

    const config = statusConfig[status] || { color: 'bg-gray-100 text-gray-800', label: status || 'Non défini' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority) => {
    const priorityConfig = {
      'Haute': { color: 'bg-red-100 text-red-800', label: 'Haute' },
      'Moyenne': { color: 'bg-yellow-100 text-yellow-800', label: 'Moyenne' },
      'Basse': { color: 'bg-green-100 text-green-800', label: 'Basse' }
    };

    const config = priorityConfig[priority] || { color: 'bg-gray-100 text-gray-800', label: priority || 'Non définie' };
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ListChecks className="h-6 w-6 text-[#F62D51]" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Recommandations</h2>
            <p className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </p>
          </div>
        </div>
        <Button className="bg-[#F62D51] hover:bg-[#E02347]">
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle recommandation
        </Button>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher des recommandations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Liste des recommandations ({filteredRecommendations.length})</span>
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
                <p className="text-gray-500">Chargement des recommandations...</p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nom</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Priorité</TableHead>
                    <TableHead>Date d'échéance</TableHead>
                    <TableHead>Responsable</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentRecommendations.length > 0 ? (
                    currentRecommendations.map((recommendation) => (
                      <TableRow key={recommendation.id}>
                        <TableCell className="font-medium">
                          {recommendation.name || 'Sans nom'}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {recommendation.description || 'Aucune description'}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(recommendation.status)}
                        </TableCell>
                        <TableCell>
                          {getPriorityBadge(recommendation.priority)}
                        </TableCell>
                        <TableCell>
                          {recommendation.dueDate ? new Date(recommendation.dueDate).toLocaleDateString('fr-FR') : '-'}
                        </TableCell>
                        <TableCell>
                          {recommendation.responsable || '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <AlertCircle className="h-8 w-8 text-gray-400" />
                          <p className="text-gray-500">
                            {searchQuery ? 'Aucune recommandation trouvée pour cette recherche' : 'Aucune recommandation trouvée pour ce plan d\'audit'}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {filteredRecommendations.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalItems={filteredRecommendations.length}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          onPageChange={setCurrentPage}
          onItemsPerPageChange={setItemsPerPage}
          startIndex={startIndex}
          endIndex={endIndex}
        />
      )}
    </div>
  );
}

export default RecommandationsTab;
