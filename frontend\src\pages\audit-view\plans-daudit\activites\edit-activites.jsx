import { useState, useEffect, useCallback, createContext, useContext, useRef } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import {
  FileText,
  Loader2,
  Activity,
  GitBranch,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import axios from 'axios';

// Create a context to simulate the outlet context
export const OutletContext = createContext(null);

// Custom hook to access the outlet context data
export const useCustomOutletContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    throw new Error('useCustomOutletContext must be used within an OutletContextProvider');
  }
  return context;
};

function EditActivites() {
  const { activiteId, planId, missionAuditId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const API_BASE_URL = getApiBaseUrl();
  const [activite, setActivite] = useState(null);
  const [auditPlan, setAuditPlan] = useState(null);
  const [missionAudit, setMissionAudit] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch activite data
  const fetchActivite = useCallback(async (isRefresh = false) => {
    // Don't fetch if component is unmounting
    if (!isMountedRef.current) return;

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    console.log('Starting fetchActivite, isRefresh:', isRefresh);
    
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
      setIsLoading(true);
      }

      console.log('Making API request to:', `${API_BASE_URL}/audit-activities/${activiteId}`);
      const response = await axios.get(
        `${API_BASE_URL}/audit-activities/${activiteId}`,
        { 
          withCredentials: true,
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          signal: abortControllerRef.current.signal,
          timeout: 10000 // 10 second timeout
        }
      );
      
      // Don't update state if component unmounted
      if (!isMountedRef.current) return;
      
      console.log('API response received:', response.data);
      
      if (response.data?.success && response.data?.data) {
        const activityData = response.data.data;
          setActivite({
          id: activityData.id || activiteId,
          planId: planId,
          missionId: missionAuditId,
          nom: activityData.name || '',
          statut: activityData.status || 'Créé',
          dateDebut: activityData.datedebut || '',
          dateFin: activityData.datefin || '',
          description: activityData.objectif || '',
          responsable: activityData.responsable || '',
          priorite: activityData.priorite || 'Moyenne',
          progression: activityData.progression || 0,
          chargedetravailestimee: activityData.chargedetravailestimee || 0,
          chargedetravaileffective: activityData.chargedetravaileffective || 0,
          objectif: activityData.objectif || '',
          depense: activityData.depense || 0
        });

        // Fetch audit plan data if planId is available
        if (planId && !isRefresh) {
          try {
            const planResponse = await axios.get(`${API_BASE_URL}/audit-plans/${planId}`, {
              withCredentials: true,
              signal: abortControllerRef.current.signal
            });
            if (planResponse.data.success && isMountedRef.current) {
              setAuditPlan(planResponse.data.data);
            }
          } catch (planError) {
            console.error("Error fetching audit plan:", planError);
          }
        }

        // Fetch mission audit data if missionAuditId is available
        if (missionAuditId && !isRefresh) {
          try {
            const missionResponse = await axios.get(`${API_BASE_URL}/audit-missions/${missionAuditId}`, {
              withCredentials: true,
              signal: abortControllerRef.current.signal
            });
            if (missionResponse.data.success && isMountedRef.current) {
              setMissionAudit(missionResponse.data.data);
            }
          } catch (missionError) {
            console.error("Error fetching mission audit:", missionError);
          }
        }

        setError(null);
      } else {
        throw new Error(response.data?.message || 'Invalid API response structure');
      }
      } catch (error) {
      // Don't update state if component unmounted
      if (!isMountedRef.current) return;

      // Don't show error for aborted requests
      if (error.name === 'CanceledError' || error.code === 'ECONNABORTED') {
        console.log('Request was aborted');
        return;
      }

      console.error('Error in fetchActivite:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Une erreur est survenue lors du chargement de l\'activité';
      setError(errorMessage);
      toast.error(errorMessage);
    }

    // Update loading states only if component is still mounted
    if (isMountedRef.current) {
      console.log('Fetch completed, setting loading states to false');
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [activiteId, planId, missionAuditId, API_BASE_URL]);

  // Initial fetch
  useEffect(() => {
    fetchActivite(false);
  }, [fetchActivite]);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.endsWith('/constats')) return 'constats';
    if (path.endsWith('/fiches-travail')) return 'fiches-travail';
    if (path.includes('/fil-activite')) return 'fil-activite';
    if (path.includes('/workflows')) return 'workflows';
    return 'caracteristiques'; // Default tab
  }, [location.pathname]);

  // Define tabs
  const tabs = [
    { id: 'caracteristiques', label: 'Caractéristiques', icon: <FileText className="h-4 w-4" /> },
    { id: 'constats', label: 'Constats', icon: <FileText className="h-4 w-4" /> },
    { id: 'fiches-travail', label: 'Fiches de travail', icon: <FileText className="h-4 w-4" /> },
    { id: 'fil-activite', label: 'Fil d\'activité', icon: <Activity className="h-4 w-4" /> },
    { id: 'workflows', label: 'Workflows', icon: <GitBranch className="h-4 w-4" /> },
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    let baseUrl;
    if (planId && missionAuditId) {
      baseUrl = `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}`;
    } else {
      baseUrl = `/audit/missions-audits/${missionAuditId}/activites/${activiteId}`;
    }
    switch (tabId) {
      case 'caracteristiques':
        navigate(baseUrl);
        break;
      case 'constats':
        navigate(`${baseUrl}/constats`);
        break;
      case 'fiches-travail':
        navigate(`${baseUrl}/fiches-travail`);
        break;
      case 'fil-activite':
        navigate(`${baseUrl}/fil-activite`);
        break;
      case 'workflows':
        navigate(`${baseUrl}/workflows`);
        break;
      default:
        navigate(baseUrl);
    }
  };

  // TabBar component inline
  const TabBar = () => (
    <div className="bg-white rounded-lg shadow-sm mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => navigateToTab(tab.id)}
              className={`
                px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center
                ${getCurrentTab() === tab.id
                  ? 'border-[#F62D51] text-[#F62D51]'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}
              `}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );

  // Add date formatting function
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#F62D51]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
              Retour
            </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Erreur</h2>
          <p className="text-gray-600">{error}</p>
        </div>
          </div>
    );
  }

  if (!activite) {
    return (
      <div className="p-4">
        <Button variant="outline" onClick={() => navigate(-1)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Retour
        </Button>
        <div className="text-center py-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-2">Activité non trouvée</h2>
          <p className="text-gray-600">L'activité demandée n'existe pas ou a été supprimée.</p>
        </div>
      </div>
    );
  }

  // Get status badge details
  const getStatusBadgeInfo = () => {
    switch (activite.statut) {
      case 'En cours':
        return { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' };
      case 'Terminé':
        return { label: 'Terminée', variant: 'default', color: 'bg-green-100 text-green-800' };
      case 'Planifié':
        return { label: 'Planifiée', variant: 'default', color: 'bg-yellow-100 text-yellow-800' };
      case 'Retardé':
        return { label: 'Retardée', variant: 'default', color: 'bg-red-100 text-red-800' };
      default:
        return { label: activite.statut, variant: 'outline', color: '' };
    }
  };

  // Update metadata with formatted dates
  const metadata = [
    `Priorité: ${activite.priorite || 'N/A'}`,
    `${formatDate(activite.dateDebut)} - ${formatDate(activite.dateFin)}`,
    `Responsable: ${activite.responsable || 'N/A'}`
  ];

  // Create context value with activite data and refetch function
  const contextValue = {
    activite,
    planId,
    missionAuditId,
    isRefreshing,
    refetchActivite: () => fetchActivite(true)
  };

  const handleGoBack = () => {
    if (planId && missionAuditId) {
      navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`);
    } else if (missionAuditId) {
      navigate(`/audit/missions-audits/${missionAuditId}`);
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto space-y-6">
      <DetailHeader
        title={activite.nom}
        icon={<FileText className="h-6 w-6 text-[#F62D51]" />}
        badges={[getStatusBadgeInfo()]}
        metadata={metadata}
        onBack={handleGoBack}
        backLabel="Retour à la liste des activités"
        breadcrumb={
          <Breadcrumb>
            <BreadcrumbList className="text-sm">
              <BreadcrumbItem>
                <BreadcrumbLink
                  href="/audit/plans-daudit"
                  className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/audit/plans-daudit');
                  }}
                >
                  Audit
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              {planId && (
                <>
                  <BreadcrumbItem>
                    <BreadcrumbLink
                      href={`/audit/plans-daudit/${planId}`}
                      className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(`/audit/plans-daudit/${planId}`);
                      }}
                    >
                      {auditPlan?.name || `Plan ${planId}`}
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                </>
              )}
              <BreadcrumbItem>
                <BreadcrumbLink
                  href={planId
                    ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
                    : `/audit/missions-audits/${missionAuditId}`
                  }
                  className="text-gray-500 font-medium hover:text-blue-900 transition-colors"
                  onClick={(e) => {
                    e.preventDefault();
                    const url = planId
                      ? `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}`
                      : `/audit/missions-audits/${missionAuditId}`;
                    navigate(url);
                  }}
                >
                  {missionAudit?.name || 'Mission d\'audit'}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-gray-800 font-medium">{activite?.nom || 'Activité'}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        }
      />
      <TabBar />
        <div className="bg-white rounded-lg shadow-sm p-6">
        <Outlet context={contextValue} />
      </div>
    </div>
  );
}

export default EditActivites;